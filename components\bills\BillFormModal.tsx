import React, { useState, useEffect, useMemo } from 'react';
import { Bill, BillStatus, Supplier, Product, TaxRate } from '../../types';
import Modal from '../ui/Modal';
import Input from '../ui/Input';
import Button from '../ui/Button';
import Select from '../ui/Select';
import ProductSelector from '../ui/ProductSelector';

interface BillItem {
  productId?: string;
  description: string;
  quantity: number;
  price: number;
  total: number;
}

interface BillFormModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (bill: Omit<Bill, '_id' | '_creationTime' | 'workspaceId' | 'amount' | 'taxRate'>) => void;
  bill: Bill | null;
  suppliers: Supplier[];
  products: Product[];
  taxRates: TaxRate[];
}

interface FormErrors {
  supplierId?: string;
  issueDate?: string;
  dueDate?: string;
  items?: string[];
  general?: string;
}

const today = new Date().toISOString().split('T')[0];
const defaultDueDate = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0];

const generateBillNumber = () => `BILL-${Date.now().toString().slice(-6)}`;

const emptyItem: BillItem = { description: '', quantity: 1, price: 0, total: 0 };

const BillFormModal: React.FC<BillFormModalProps> = ({ isOpen, onClose, onSave, bill, suppliers, products, taxRates }) => {
  const [errors, setErrors] = useState<FormErrors>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const newBillTemplate = useMemo(() => {
    const defaultTax = taxRates.find(t => t.isDefault);
    return {
      supplierId: '',
      billNumber: generateBillNumber(),
      issueDate: today,
      dueDate: defaultDueDate,
      status: BillStatus.Unpaid,
      items: [emptyItem],
      taxId: defaultTax?._id || '',
      notes: '',
    }
  }, [taxRates]);

  const [formData, setFormData] = useState(newBillTemplate);
  const isEditing = useMemo(() => !!bill, [bill]);

  useEffect(() => {
    if (isOpen) {
      setErrors({});
      setIsSubmitting(false);
      if (bill) {
        const { _id, _creationTime, workspaceId, amount, taxRate, ...editableData } = bill;
        setFormData({
          supplierId: editableData.supplierId || '',
          billNumber: editableData.billNumber || generateBillNumber(),
          issueDate: editableData.issueDate || editableData.date || today,
          dueDate: editableData.dueDate || today,
          status: editableData.status || BillStatus.Unpaid,
          items: editableData.items || [{ ...emptyItem, description: editableData.description || '' }],
          taxId: editableData.taxId || '',
          notes: editableData.notes || '',
        });
      } else {
        setFormData({ ...newBillTemplate, items: [{ ...emptyItem }] });
      }
    }
  }, [bill, isOpen, newBillTemplate]);

  const validateForm = () => {
    const newErrors: FormErrors = {};

    if (!formData.supplierId) {
      newErrors.supplierId = 'Supplier is required';
    }

    if (!formData.issueDate) {
      newErrors.issueDate = 'Issue date is required';
    }

    if (!formData.dueDate) {
      newErrors.dueDate = 'Due date is required';
    }

    const itemErrors: string[] = [];
    formData.items.forEach((item, index) => {
      if (!item.description.trim()) {
        itemErrors[index] = 'Description is required';
      } else if (item.quantity <= 0) {
        itemErrors[index] = 'Quantity must be greater than 0';
      } else if (item.price < 0) {
        itemErrors[index] = 'Price cannot be negative';
      }
    });

    if (itemErrors.length > 0) {
      newErrors.items = itemErrors;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const selectedTax = taxRates.find(t => t._id === formData.taxId);
      const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0);
      const taxAmount = selectedTax ? subtotal * (selectedTax.rate / 100) : 0;
      const totalAmount = subtotal + taxAmount;

      const billData = {
        ...formData,
        amount: totalAmount,
        taxRate: selectedTax?.rate
      };

      await onSave(billData);
      onClose();
    } catch (error) {
      console.error('Failed to save bill:', error);
      setErrors({ general: 'Failed to save bill. Please try again.' });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleItemChange = (index: number, field: keyof BillItem, value: string | number) => {
    const newItems = [...formData.items];
    newItems[index] = { ...newItems[index], [field]: value };

    if (field === 'quantity' || field === 'price') {
      newItems[index].total = newItems[index].quantity * newItems[index].price;
    }

    setFormData(prev => ({ ...prev, items: newItems }));
  };

  const handleProductSelect = (index: number, description: string, product?: Product) => {
    const newItems = [...formData.items];
    if (product) {
      newItems[index] = {
        ...newItems[index],
        productId: product._id,
        description: product.name,
        price: product.unitPrice,
        total: newItems[index].quantity * product.unitPrice
      };
    } else {
      newItems[index] = {
        ...newItems[index],
        productId: undefined,
        description,
        total: newItems[index].quantity * newItems[index].price
      };
    }
    setFormData(prev => ({ ...prev, items: newItems }));
  };

  const addItem = () => {
    setFormData(prev => ({
      ...prev,
      items: [...prev.items, { ...emptyItem }]
    }));
  };

  const removeItem = (index: number) => {
    if (formData.items.length > 1) {
      const newItems = formData.items.filter((_, i) => i !== index);
      setFormData(prev => ({ ...prev, items: newItems }));
    }
  };

  const formatCurrency = (amount: number) => new Intl.NumberFormat('en-US', { style: 'currency', currency: 'USD' }).format(amount);

  const subtotal = formData.items.reduce((sum, item) => sum + item.total, 0);
  const selectedTax = taxRates.find(t => t._id === formData.taxId);
  const taxAmount = selectedTax ? subtotal * (selectedTax.rate / 100) : 0;
  const totalAmount = subtotal + taxAmount;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="3xl">
      <div className="flex flex-col max-h-[90vh]">
        {/* Header */}
        <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 px-4 py-4 relative overflow-hidden">
          <div className="absolute inset-0 bg-black/20"></div>
          <div className="relative z-10 flex items-center justify-between">
            <div>
              <h1 className="text-lg font-bold tracking-tight text-white">
                {isEditing ? 'Edit Bill' : 'Create New Bill'}
              </h1>
              <p className="text-white/70 text-xs font-medium">
                {isEditing ? 'Update bill details and items' : 'Add a new bill to your records'}
              </p>
            </div>

            <div className="text-right space-y-1">
              <div className="text-white/60 text-xs font-medium uppercase tracking-wider">Bill Number</div>
              <div className="text-lg font-bold text-white font-mono tracking-wide bg-white/10 backdrop-blur-sm px-3 py-1 rounded-lg border border-white/20">
                {formData.billNumber}
              </div>
            </div>
          </div>

          <div className="mt-3 flex items-center justify-between">
            <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold bg-white/10 backdrop-blur-sm text-white border border-white/20">
              <div className="w-1.5 h-1.5 bg-blue-400 rounded-full mr-2 animate-pulse"></div>
              {isEditing ? 'Editing Mode' : 'Creation Mode'}
            </div>
            <button
              type="button"
              onClick={onClose}
              className="text-white/60 hover:text-white hover:bg-white/10 rounded-lg p-1.5 transition-all duration-200"
            >
              <svg className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="flex flex-col flex-1">
          <div className="flex-1 overflow-y-auto">
            <div className="p-4 space-y-4">
              {/* Error Display */}
              {errors.general && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3 shadow-sm">
                  <div className="flex">
                    <div className="flex-shrink-0">
                      <svg className="h-4 w-4 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                      </svg>
                    </div>
                    <div className="ml-2">
                      <p className="text-xs text-red-800">{errors.general}</p>
                    </div>
                  </div>
                </div>
              )}

              {/* Supplier & Bill Details */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2 pb-2 border-b border-gray-100">
                  <div className="w-6 h-6 bg-gray-900 rounded-lg flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h4M9 7h6m-6 4h6m-6 4h6" />
                    </svg>
                  </div>
                  <h4 className="text-sm font-bold text-gray-900">Supplier & Bill Details</h4>
                </div>
                <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-1">
                      <Select
                        label="Supplier *"
                        name="supplierId"
                        value={formData.supplierId}
                        onChange={handleChange}
                        className={`text-sm transition-all duration-200 ${errors.supplierId ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900'}`}
                      >
                        <option value="" disabled>Select a supplier</option>
                        {suppliers.map(supplier => (
                          <option key={supplier._id} value={supplier._id}>{supplier.name}</option>
                        ))}
                      </Select>
                      {errors.supplierId && (
                        <div className="flex items-center space-x-1 text-red-600">
                          <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p className="text-xs font-medium">{errors.supplierId}</p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-1">
                      <Select
                        label="Status"
                        name="status"
                        value={formData.status}
                        onChange={handleChange}
                        className="text-sm focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200"
                      >
                        {Object.values(BillStatus).map(s => (
                          <option key={s} value={s}>{s}</option>
                        ))}
                      </Select>
                    </div>

                    <div className="space-y-1">
                      <Input
                        label="Issue Date *"
                        type="date"
                        name="issueDate"
                        value={formData.issueDate}
                        onChange={handleChange}
                        className={`text-sm transition-all duration-200 ${errors.issueDate ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900'}`}
                      />
                      {errors.issueDate && (
                        <div className="flex items-center space-x-1 text-red-600">
                          <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p className="text-xs font-medium">{errors.issueDate}</p>
                        </div>
                      )}
                    </div>

                    <div className="space-y-1">
                      <Input
                        label="Due Date *"
                        type="date"
                        name="dueDate"
                        value={formData.dueDate}
                        onChange={handleChange}
                        className={`text-sm transition-all duration-200 ${errors.dueDate ? 'border-red-300 focus:border-red-500 focus:ring-red-500' : 'focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900'}`}
                      />
                      {errors.dueDate && (
                        <div className="flex items-center space-x-1 text-red-600">
                          <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                          <p className="text-xs font-medium">{errors.dueDate}</p>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              </div>

              {/* Bill Items */}
              <div className="space-y-3">
                <div className="flex items-center justify-between pb-2 border-b border-gray-100">
                  <div className="flex items-center space-x-2">
                    <div className="w-6 h-6 bg-gray-900 rounded-lg flex items-center justify-center">
                      <svg className="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                      </svg>
                    </div>
                    <h4 className="text-sm font-bold text-gray-900">Bill Items</h4>
                  </div>
                  <Button
                    type="button"
                    variant="primary"
                    size="sm"
                    onClick={addItem}
                    className="flex items-center gap-1 bg-gray-900 hover:bg-gray-800 text-xs px-2 py-1 shadow-lg hover:shadow-xl transition-all duration-200"
                  >
                    <svg className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    Add Item
                  </Button>
                </div>
                <div className="bg-white border border-gray-200 rounded-lg shadow-sm overflow-hidden">
                  {/* Table Header */}
                  <div className="bg-gradient-to-r from-gray-900 via-gray-800 to-gray-900 px-4 py-3">
                    <div className="grid grid-cols-12 gap-3 text-xs font-bold text-white uppercase tracking-wider">
                      <div className="col-span-5 flex items-center space-x-1">
                        <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                        </svg>
                        <span>Product/Description</span>
                      </div>
                      <div className="col-span-2 text-center">Quantity</div>
                      <div className="col-span-2 text-center">Unit Price</div>
                      <div className="col-span-2 text-center">Total</div>
                      <div className="col-span-1 text-center">Actions</div>
                    </div>
                  </div>

                  {/* Table Body */}
                  <div className="divide-y divide-gray-50">
                    {formData.items.map((item, index) => (
                      <div key={index} className="group hover:bg-gray-50/50 transition-all duration-200">
                        <div className="px-4 py-3">
                          <div className="grid grid-cols-12 gap-3 items-center">
                            {/* Product/Description */}
                            <div className="col-span-5">
                              <div className="space-y-1">
                                <div className="relative">
                                  <ProductSelector
                                    value={item.description}
                                    onChange={(value, product) => handleProductSelect(index, value, product)}
                                    products={products}
                                    placeholder="Search products or enter description..."
                                  />
                                </div>
                                {errors.items && errors.items[index] && (
                                  <div className="flex items-center gap-1 text-red-600 bg-red-50 px-2 py-1 rounded border border-red-200">
                                    <svg className="h-3 w-3 flex-shrink-0" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                    <p className="text-xs font-medium">{errors.items[index]}</p>
                                  </div>
                                )}
                              </div>
                            </div>

                            {/* Quantity */}
                            <div className="col-span-2">
                              <div className="relative group">
                                <input
                                  type="number"
                                  min="1"
                                  step="1"
                                  value={item.quantity}
                                  onChange={(e) => handleItemChange(index, 'quantity', parseInt(e.target.value) || 1)}
                                  className="w-full px-2 py-2 text-center border border-gray-300 rounded-lg shadow-sm text-sm font-semibold text-gray-900 bg-white focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200"
                                />
                                <div className="absolute inset-y-0 right-0 pr-2 flex items-center pointer-events-none">
                                  <span className="text-gray-400 text-xs font-medium">QTY</span>
                                </div>
                              </div>
                            </div>

                            {/* Unit Price */}
                            <div className="col-span-2">
                              <div className="relative group">
                                <div className="absolute inset-y-0 left-0 pl-2 flex items-center pointer-events-none">
                                  <span className="text-gray-500 text-xs font-semibold">$</span>
                                </div>
                                <input
                                  type="number"
                                  min="0"
                                  step="0.01"
                                  value={item.price}
                                  onChange={(e) => handleItemChange(index, 'price', parseFloat(e.target.value) || 0)}
                                  className="w-full pl-6 pr-2 py-2 text-center border border-gray-300 rounded-lg shadow-sm text-sm font-semibold text-gray-900 bg-white focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200"
                                />
                              </div>
                            </div>

                            {/* Total */}
                            <div className="col-span-2">
                              <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-lg px-2 py-2 text-center shadow-sm">
                                <span className="font-bold text-white text-sm tracking-wide">
                                  {formatCurrency(item.total)}
                                </span>
                              </div>
                            </div>

                            {/* Actions */}
                            <div className="col-span-1 flex justify-center">
                              <button
                                type="button"
                                onClick={() => removeItem(index)}
                                disabled={formData.items.length === 1}
                                className="p-1 text-red-600 hover:text-red-800 hover:bg-red-50 rounded transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                                title="Remove item"
                              >
                                <svg className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                </svg>
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Table Footer */}
                  <div className="bg-gradient-to-r from-gray-900 to-gray-800 px-4 py-2">
                    <div className="flex justify-between items-center">
                      <div className="flex items-center space-x-1 text-white/80">
                        <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                        <span className="text-xs font-medium">{formData.items.length} item{formData.items.length !== 1 ? 's' : ''} added</span>
                      </div>
                      <div className="text-white">
                        <span className="text-xs font-medium">Subtotal: </span>
                        <span className="text-sm font-bold">{formatCurrency(subtotal)}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Tax Settings & Summary */}
              <div className="space-y-3">
                <div className="flex items-center space-x-2 pb-2 border-b border-gray-100">
                  <div className="w-6 h-6 bg-gray-900 rounded-lg flex items-center justify-center">
                    <svg className="w-3 h-3 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 0v5a2 2 0 01-2 2H9a2 2 0 01-2-2V7a2 2 0 012-2h6a2 2 0 012 2z" />
                    </svg>
                  </div>
                  <h4 className="text-sm font-bold text-gray-900">Bill Summary</h4>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {/* Tax Settings */}
                  <div className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md transition-shadow duration-200">
                    <h5 className="text-sm font-bold text-gray-900 mb-3 flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 0v5a2 2 0 01-2 2H9a2 2 0 01-2-2V7a2 2 0 012-2h6a2 2 0 012 2z" />
                      </svg>
                      <span>Tax Settings</span>
                    </h5>

                    <div className="space-y-3">
                      <div>
                        <label className="block text-xs font-semibold text-gray-700 mb-1">Tax Rate</label>
                        <Select
                          name="taxId"
                          value={formData.taxId}
                          onChange={handleChange}
                          className="w-full text-sm focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200"
                        >
                          <option value="">No Tax</option>
                          {taxRates.map(t => (
                            <option key={t._id} value={t._id}>
                              {t.name} ({t.rate}%)
                            </option>
                          ))}
                        </Select>
                      </div>

                      <div>
                        <label className="block text-xs font-semibold text-gray-700 mb-1">Notes</label>
                        <textarea
                          name="notes"
                          value={formData.notes || ''}
                          onChange={handleChange}
                          rows={3}
                          className="w-full px-3 py-2 text-sm border border-gray-300 rounded-lg shadow-sm bg-white focus:ring-2 focus:ring-gray-900/20 focus:border-gray-900 transition-all duration-200"
                          placeholder="Add any additional notes or comments..."
                        />
                      </div>
                    </div>
                  </div>

                  {/* Summary */}
                  <div className="space-y-3">
                    <h5 className="text-sm font-bold text-gray-900 flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 0v5a2 2 0 01-2 2H9a2 2 0 01-2-2V7a2 2 0 012-2h6a2 2 0 012 2z" />
                      </svg>
                      <span>Bill Summary</span>
                    </h5>

                    <div className="bg-gradient-to-br from-white to-gray-50 rounded-lg border border-gray-200 p-4 space-y-3 shadow-sm">
                      {/* Subtotal */}
                      <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">Subtotal</span>
                        <span className="text-sm font-bold text-gray-900">{formatCurrency(subtotal)}</span>
                      </div>

                      {/* Tax */}
                      <div className="flex justify-between items-center pb-2 border-b border-gray-100">
                        <span className="text-xs font-semibold text-gray-600 uppercase tracking-wide">
                          Tax {selectedTax ? `(${selectedTax.name} - ${selectedTax.rate}%)` : '(0%)'}
                        </span>
                        <span className="text-sm font-bold text-gray-900">{formatCurrency(taxAmount)}</span>
                      </div>

                      {/* Total */}
                      <div className="bg-gradient-to-r from-gray-900 to-gray-800 rounded-lg p-3 -m-1 mt-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-bold text-white uppercase tracking-wide">Total Amount</span>
                          <span className="text-lg font-bold text-white">{formatCurrency(totalAmount)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div className="flex-shrink-0 bg-gradient-to-r from-gray-50 to-white border-t border-gray-200 px-4 py-3">
            <div className="flex justify-between items-center">
              <div className="flex items-center space-x-1 text-gray-600">
                <svg className="w-3 h-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span className="text-xs font-medium">
                  {isEditing ? 'Update bill details and save changes' : 'Review all details before creating the bill'}
                </span>
              </div>
              <div className="flex space-x-2">
                <Button
                  type="button"
                  variant="secondary"
                  onClick={onClose}
                  disabled={isSubmitting}
                  className="px-4 py-2 text-sm border-2 border-gray-300 hover:border-gray-400 transition-all duration-200"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  variant="primary"
                  disabled={isSubmitting}
                  className="min-w-[120px] px-6 py-2 text-sm bg-gray-900 hover:bg-gray-800 shadow-lg hover:shadow-xl transition-all duration-200"
                >
                  {isSubmitting ? (
                    <div className="flex items-center space-x-1">
                      <svg className="animate-spin h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      <span className="font-semibold">Saving...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-1">
                      <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                      <span className="font-semibold">
                        {isEditing ? 'Update Bill' : 'Create Bill'}
                      </span>
                    </div>
                  )}
                </Button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </Modal>
  );
};

export default BillFormModal;
