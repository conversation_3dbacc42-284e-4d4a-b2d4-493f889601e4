// --- Base DB Record Types ---
interface BaseDoc {
  _id: string;
  _creationTime: number;
  id?: string; // Optional to avoid breaking other parts, but helps with Table type constraints
}

// --- Redefined Convex Doc types for convenience ---
export interface User extends BaseDoc {
  name: string;
  email: string;
  tokenIdentifier: string;
}

export interface Workspace extends BaseDoc {
  name: string;
  ownerId: string;
}

export interface TeamMember extends BaseDoc {
  workspaceId: string;
  userId: string;
  name: string;
  email: string;
  role: UserRole;
  status: TeamMemberStatus;
}

export interface Invoice extends BaseDoc {
  workspaceId: string;
  clientId: string;
  invoiceNumber?: string;
  issueDate: string;
  dueDate: string;
  amount: number;
  status: InvoiceStatus;
  items: InvoiceItem[];
  taxId?: string;
  taxRate?: number;
  notes?: string;
  discountType?: 'percentage' | 'fixed';
  discountValue?: number;
}
export type InvoiceItem = {
    productId?: string;
    description: string;
    quantity: number;
    unitPrice: number;
    total: number;
};

export interface Bill extends BaseDoc {
  workspaceId: string;
  supplierId?: string;
  billNumber?: string;
  issueDate: string;
  dueDate: string;
  amount: number;
  status: BillStatus;
  items?: BillItem[];
  taxId?: string;
  taxRate?: number;
  notes?: string;
  // Legacy fields for backward compatibility
  vendor?: string;
  category?: string;
  description?: string;
  date?: string;
  receiptUrl?: string;
}

export type BillItem = {
  productId?: string;
  description: string;
  quantity: number;
  price: number;
  total: number;
};

export interface Product extends BaseDoc {
  workspaceId: string;
  name: string;
  category: string;
  sku: string;
  stock: number;
  unitPrice: number;
}

export interface Quote extends BaseDoc {
  workspaceId: string;
  clientId: string;
  quoteNumber?: string;
  issueDate: string;
  expiryDate: string;
  amount: number;
  status: QuoteStatus;
  items: InvoiceItem[];
  taxId?: string;
  taxRate?: number;
}

export interface Account extends BaseDoc {
  workspaceId: string;
  code: string;
  name: string;
  type: AccountType;
  balance: number;
}

export interface JournalEntry extends BaseDoc {
  workspaceId: string;
  date: string;
  description: string;
  debits: JournalDetail[];
  credits: JournalDetail[];
  origin?: {
      type: 'invoice' | 'bill';
      id: string;
  };
}
export type JournalDetail = {
    accountId: string;
    accountName: string;
    amount: number;
};

export interface Client extends BaseDoc {
  workspaceId: string;
  name: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface Supplier extends BaseDoc {
  workspaceId: string;
  name: string;
  email: string;
  category: string;
  address: string;
  phone: string;
}

export interface PurchaseOrder extends BaseDoc {
  workspaceId: string;
  supplierId: string;
  poNumber?: string;
  orderDate: string;
  expectedDelivery: string;
  amount: number;
  status: PurchaseOrderStatus;
  items: PurchaseItem[];
  taxId?: string;
  taxRate?: number;
  notes?: string;
}
export type PurchaseItem = {
  productId?: string;
  description: string;
  quantity: number;
  unitPrice: number;
  total: number;
};

export interface TaxRate extends BaseDoc {
  workspaceId: string;
  name: string;
  rate: number;
  isDefault?: boolean;
}

export interface CompanyProfile extends BaseDoc {
  workspaceId: string;
  name: string;
  address: string;
  email: string;
  phone: string;
  logoUrl: string;
}


// --- Enums and other non-DB types ---

export type Page = 'Dashboard' | 'Invoices' | 'Bills' | 'Inventory' | 'Reports' | 'Settings' | 'Quotes' | 'Transactions' | 'Chart of Accounts' | 'Team' | 'Clients' | 'Purchases' | 'Suppliers';

export enum UserRole {
    Admin = 'Admin',
    Member = 'Member',
}
export type TeamMemberStatus = 'Active' | 'Invited';

export enum InvoiceStatus {
  Draft = 'Draft',
  Sent = 'Sent',
  Paid = 'Paid',
  Overdue = 'Overdue',
}

export enum BillStatus {
  Unpaid = 'Unpaid',
  Paid = 'Paid',
  Overdue = 'Overdue',
}

export enum QuoteStatus {
    Draft = 'Draft',
    Sent = 'Sent',
    Accepted = 'Accepted',
    Declined = 'Declined',
}

export enum PurchaseOrderStatus {
    Draft = 'Draft',
    Ordered = 'Ordered',
    Completed = 'Completed',
    Cancelled = 'Cancelled',
}

export enum AccountType {
    Asset = 'Asset',
    Liability = 'Liability',
    Equity = 'Equity',
    Revenue = 'Revenue',
    Expense = 'Expense',
}


// --- Page Props ---
export interface PageProps {
  workspaceId: string;
}
export interface NavigablePageProps extends PageProps {
  navigate: (page: Page, context?: any) => void;
}
export interface ContextAwarePageProps extends PageProps {
  context: any;
  clearPageContext: () => void;
}

export type SettingsPage = 'Profile' | 'Company' | 'Billing' | 'Taxes' | 'API Keys' | 'Integrations';

// --- Other UI-related types ---
export interface CashFlowData {
  month: string;
  income: number;
  expenses: number;
}

export interface ProfitLossData {
  category: string;
  amount: number;
}

export interface AIMessage {
  role: 'user' | 'model';
  text: string;
  timestamp: string;
}

// --- Report Data Types ---
export interface ReportCategoryData {
  [category: string]: {
    accounts: { name: string; balance: number }[];
    total: number;
  };
}

export interface PAndLData {
  revenue: ReportCategoryData;
  expenses: ReportCategoryData;
  netIncome: number;
}

export interface BalanceSheetData {
    assets: ReportCategoryData;
    liabilities: ReportCategoryData;
    equity: ReportCategoryData;
    totalAssets: number;
    totalLiabilitiesAndEquity: number;
}

export interface CashFlowTransaction {
  date: string;
  description: string;
  amount: number;
}

export interface CashFlowReportData {
  inflows: CashFlowTransaction[];
  outflows: CashFlowTransaction[];
  totalInflow: number;
  totalOutflow: number;
  netCashFlow: number;
}

export interface TaxTransaction {
    date: string;
    source: string; // e.g., "Invoice INV-003"
    taxAmount: number;
}

export interface TaxReportData {
    transactions: TaxTransaction[];
    totalTax: number;
}